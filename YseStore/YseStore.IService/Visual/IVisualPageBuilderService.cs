using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Response.Visual;

namespace YseStore.IService.Visual
{
    /// <summary>
    /// 可视化页面构建服务接口
    /// </summary>
    public interface IVisualPageBuilderService
    {
        /// <summary>
        /// 根据页面类型构建可视化页面数据
        /// </summary>
        /// <param name="pages">页面类型（如：index、products等）</param>
        /// <returns></returns>
        Task<VisualPageResponse> BuildVisualPageAsync(string pages);

        /// <summary>
        /// 获取页面插件配置数据
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        Task<Dictionary<string, object>> GetPagePluginConfigAsync(string pages);
    }
}
