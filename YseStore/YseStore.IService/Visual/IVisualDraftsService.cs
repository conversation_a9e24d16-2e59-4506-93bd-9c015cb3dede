using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.IService.Visual
{
    public interface IVisualDraftsService : IBaseServices<visual_drafts>
    {
        /// <summary>
        /// 获取最新的可见草稿
        /// </summary>
        /// <returns></returns>
        Task<visual_drafts> GetLatestVisibleDraftAsync();
    }
}
