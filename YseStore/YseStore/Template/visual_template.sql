/*
 Navicat Premium Data Transfer

 Source Server         : *************(本地官网)
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : *************:3306
 Source Schema         : ysestoredb

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 29/07/2025 14:42:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for visual_template
-- ----------------------------
DROP TABLE IF EXISTS `visual_template`;
CREATE TABLE `visual_template`  (
  `TemplateId` int NOT NULL AUTO_INCREMENT,
  `DraftsId` int NULL DEFAULT 0 COMMENT '草稿箱id',
  `Pages` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '页面',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容',
  PRIMARY KEY (`TemplateId`) USING BTREE,
  INDEX `DraftsId`(`DraftsId`) USING BTREE,
  INDEX `TemplateId`(`TemplateId`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1500 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of visual_template
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
