/*
 Navicat Premium Data Transfer

 Source Server         : *************(本地官网)
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : *************:3306
 Source Schema         : ysestoredb

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 29/07/2025 14:41:49
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for visual_pages
-- ----------------------------
DROP TABLE IF EXISTS `visual_pages`;
CREATE TABLE `visual_pages`  (
  `PagesId` int NOT NULL AUTO_INCREMENT,
  `DraftsId` int NULL DEFAULT 0,
  `Pages` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `AssociationId` int NULL DEFAULT 0 COMMENT '关联Id',
  `AssociationIdPlus` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Plugins` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '页面插件',
  `TemplateId` int NULL DEFAULT 0 COMMENT '模板id',
  `IsDeal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已处理',
  PRIMARY KEY (`PagesId`) USING BTREE,
  INDEX `DraftsId`(`DraftsId`) USING BTREE,
  INDEX `TemplateId`(`TemplateId`) USING BTREE,
  INDEX `DraftsId_Pages_AssociationId`(`DraftsId`, `Pages`, `AssociationId`) USING BTREE,
  INDEX `Pages_AssociationId`(`Pages`, `AssociationId`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 11408 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of visual_pages
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
