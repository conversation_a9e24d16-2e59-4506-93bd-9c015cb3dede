using Microsoft.AspNetCore.Mvc;
using YseStore.IService.Store;
using YseStore.IService.Visual;
using YseStore.Model.Response.Visual;

namespace YseStore.Controllers
{
    /// <summary>
    /// 可视化页面控制器
    /// </summary>
    public class VisualController : BaseController
    {
        private readonly IVisualPageBuilderService _visualPageBuilderService;

        public VisualController(IMenuService menuService, IVisualPageBuilderService visualPageBuilderService) 
            : base(menuService)
        {
            _visualPageBuilderService = visualPageBuilderService;
        }

        /// <summary>
        /// 获取可视化页面数据
        /// </summary>
        /// <param name="pages">页面类型（如：index、products等）</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPageData(string pages = "index")
        {
            try
            {
                if (string.IsNullOrEmpty(pages))
                {
                    return Json(new { ret = 0, msg = "页面类型不能为空" });
                }

                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync(pages);
                
                return Json(new { ret = 1, data = visualPageData });
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"获取可视化页面数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return Json(new { ret = 0, msg = "获取页面数据失败" });
            }
        }

        /// <summary>
        /// 获取页面插件配置
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPageConfig(string pages = "index")
        {
            try
            {
                if (string.IsNullOrEmpty(pages))
                {
                    return Json(new { ret = 0, msg = "页面类型不能为空" });
                }

                var config = await _visualPageBuilderService.GetPagePluginConfigAsync(pages);
                
                return Json(new { ret = 1, data = config });
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"获取页面插件配置时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return Json(new { ret = 0, msg = "获取页面配置失败" });
            }
        }

        /// <summary>
        /// 根据插件类型获取插件列表
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <param name="type">插件类型</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetPluginsByType(string pages = "index", string type = "")
        {
            try
            {
                if (string.IsNullOrEmpty(pages))
                {
                    return Json(new { ret = 0, msg = "页面类型不能为空" });
                }

                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync(pages);
                
                if (string.IsNullOrEmpty(type))
                {
                    // 返回所有插件按类型分组
                    return Json(new { ret = 1, data = visualPageData.PluginsByType });
                }
                else
                {
                    // 返回指定类型的插件
                    var plugins = visualPageData.PluginsByType.ContainsKey(type) 
                        ? visualPageData.PluginsByType[type] 
                        : new List<Entitys.visual_plugins>();
                    
                    return Json(new { ret = 1, data = plugins });
                }
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"获取插件数据时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                return Json(new { ret = 0, msg = "获取插件数据失败" });
            }
        }
    }
}
