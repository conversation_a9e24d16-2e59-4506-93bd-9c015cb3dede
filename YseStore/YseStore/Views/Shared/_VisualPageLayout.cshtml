@*
    可视化页面布局模板
    用于展示基于数据库配置的可视化页面内容
*@

@{
    var visualData = ViewBag.VisualPageData as YseStore.Model.Response.Visual.VisualPageResponse;
}

<!-- 可视化页面容器 -->
<div class="visual-page-container" data-page-type="@(ViewBag.PageType ?? "index")">
    
    @if (visualData != null && visualData.Plugins.Any())
    {
        <!-- 根据插件类型渲染不同的区域 -->
        @foreach (var pluginGroup in visualData.PluginsByType)
        {
            <div class="visual-section" data-plugin-type="@pluginGroup.Key">
                @switch (pluginGroup.Key)
                {
                    case "carousel":
                        <!-- 轮播图区域 -->
                        <div class="carousel-section">
                            @foreach (var plugin in pluginGroup.Value)
                            {
                                <div class="carousel-container" data-plugin-id="@plugin.PId">
                                    <!-- 轮播图内容将通过JavaScript动态加载 -->
                                </div>
                            }
                        </div>
                        break;
                        
                    case "banner":
                        <!-- 横幅区域 -->
                        <div class="banner-section">
                            @foreach (var plugin in pluginGroup.Value)
                            {
                                <div class="banner-container" data-plugin-id="@plugin.PId">
                                    <!-- 横幅内容将通过JavaScript动态加载 -->
                                </div>
                            }
                        </div>
                        break;
                        
                    case "products":
                        <!-- 产品展示区域 -->
                        <div class="products-section">
                            @foreach (var plugin in pluginGroup.Value)
                            {
                                <div class="products-container" data-plugin-id="@plugin.PId">
                                    <!-- 产品内容将通过JavaScript动态加载 -->
                                </div>
                            }
                        </div>
                        break;
                        
                    case "text":
                        <!-- 文本区域 -->
                        <div class="text-section">
                            @foreach (var plugin in pluginGroup.Value)
                            {
                                <div class="text-container" data-plugin-id="@plugin.PId">
                                    <!-- 文本内容将通过JavaScript动态加载 -->
                                </div>
                            }
                        </div>
                        break;
                        
                    default:
                        <!-- 其他类型插件 -->
                        <div class="custom-section">
                            @foreach (var plugin in pluginGroup.Value)
                            {
                                <div class="custom-container" data-plugin-id="@plugin.PId" data-plugin-type="@plugin.Type">
                                    <!-- 自定义内容将通过JavaScript动态加载 -->
                                </div>
                            }
                        </div>
                        break;
                }
            </div>
        }
    }
    else
    {
        <!-- 没有可视化数据时的默认内容 -->
        <div class="no-visual-data">
            <p>暂无可视化页面配置</p>
        </div>
    }
</div>

<!-- 可视化页面样式 -->
<style>
.visual-page-container {
    width: 100%;
    min-height: 100vh;
}

.visual-section {
    width: 100%;
    margin-bottom: 20px;
}

.carousel-section,
.banner-section,
.products-section,
.text-section,
.custom-section {
    width: 100%;
}

.carousel-container,
.banner-container,
.products-container,
.text-container,
.custom-container {
    width: 100%;
    min-height: 200px;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    position: relative;
}

.carousel-container::before,
.banner-container::before,
.products-container::before,
.text-container::before,
.custom-container::before {
    content: attr(data-plugin-type) " 插件区域";
    color: #999;
    font-size: 14px;
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 8px;
    border-radius: 3px;
}

.no-visual-data {
    text-align: center;
    padding: 50px;
    color: #999;
}

/* 轮播图样式 */
.visual-carousel {
    width: 100%;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.carousel-item {
    width: 100%;
    height: 100%;
    position: relative;
    display: none;
}

.carousel-item:first-child {
    display: block;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 2;
}

.carousel-content h2 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.carousel-content p {
    font-size: 1.2em;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.carousel-btn {
    background: #ff830a;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.carousel-btn:hover {
    background: #e6740a;
}
</style>

<!-- 引入可视化页面构建器脚本 -->
<script src="~/assets/js/visual-page-builder.js"></script>

<script>
// 页面特定的初始化代码
document.addEventListener('DOMContentLoaded', function() {
    // 如果有服务器端数据，可以直接传递给前端
    @if (visualData != null)
    {
        <text>
        window.serverVisualData = @Html.Raw(Json.Serialize(visualData));
        console.log('服务器端可视化数据:', window.serverVisualData);
        </text>
    }
});
</script>
