<!--Home slider-->

<div class=" visual_plugins_container" data-type="carousel" data-mode="mode_1" data-location="2">
    <div class="slideshow slideshow-wrapper" data-visual-id="17773">
        <div class="home-slideshow">
            {% comment %} 使用可视化数据渲染轮播图 {% endcomment %}
            {% assign carousel_plugins = VisualPageData | get_visual_plugins: 'carousel' %}
            {% if carousel_plugins.size > 0 %}
                {% for plugin in carousel_plugins %}
                    {% assign plugin_visible = plugin | is_plugin_visible %}
                    {% if plugin_visible %}
                        {% assign plugin_blocks = plugin | get_plugin_blocks %}
                        {% assign plugin_settings = plugin | get_plugin_settings %}
                        {% for block_item in plugin_blocks %}
                            {% assign block = block_item[1] %}
                            {% if block.ContentType == 'images' %}
                            <div class="slide">
                                <div class="blur-up lazyload">
                                    {% if IsUseDesktop %}
                                        <img class="blur-up lazyload desktop-hide sw-slide"
                                             data-src="{{ block.PicPc }}"
                                             src="{{ block.PicPc }}"
                                             alt="{{ block.ImageAltPc | default: block.Title }}"
                                             title="{{ block.ImageAltPc | default: block.Title }}" />
                                    {% else %}
                                        <img class="blur-up lazyload mobile-hide sw-slide"
                                             data-src="{{ block.PicMobile | default: block.PicPc }}"
                                             src="{{ block.PicMobile | default: block.PicPc }}"
                                             alt="{{ block.ImageAltMobile | default: block.Title }}"
                                             title="{{ block.ImageAltMobile | default: block.Title }}" />
                                    {% endif %}
                                    {% if block.Title or block.Content or block.ButtonText %}
                                    <div class="slideshow__text-wrap slideshow__overlay {{ block.TextPosition | default: 'center' }}">
                                        <div class="slideshow__text-content">
                                            <div class="slide-content">
                                                {% if block.Title %}
                                                <h2 class="slide-title" style="color: {{ block.TitleColor | default: '#ffffff' }}; font-size: {{ block.TitleFontSizePc | default: '48px' }};">{{ block.Title }}</h2>
                                                {% endif %}
                                                {% if block.Content %}
                                                <p class="slide-desc" style="color: {{ block.ContentColor | default: '#ffffff' }}; font-size: {{ block.ContentFontSizePc | default: '16px' }};">{{ block.Content }}</p>
                                                {% endif %}
                                                {% if block.ButtonText and block.Link %}
                                                <a href="{{ block.Link }}" class="slide-button"
                                                   style="background-color: {{ block.ButtonColor | default: '#ff830a' }};
                                                          color: {{ block.ButtonTextColor | default: '#ffffff' }};
                                                          border-radius: {{ block.ButtonRadiusSize | default: '20px' }};">{{ block.ButtonText }}</a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% else %}
                {% comment %} 如果没有可视化数据，显示默认轮播图 {% endcomment %}
            <!--<div class="slide">
                <div class="blur-up lazyload">
                    <img class="blur-up lazyload desktop-hide sw-slide" data-src="{{static_path}}/assets/images/index/banner/retekess2025-2.webp" src="{{static_path}}/assets/images/index/banner/retekess2025-2.webp" alt="tour guide system free demo" title="tour guide system free demo" />
                    <img class="blur-up lazyload mobile-hide sw-slide" data-src="{{static_path}}/assets/images/index/banner/retekess2025-mobile.png" src="{{static_path}}/assets/images/index//banner/retekess2025-mobile.png" alt="tour guide system free demo" title="tour guide system free demo" />
                    <div class="slideshow__text-wrap slideshow__overlay center">
                        <div class="slideshow__text-content">
                            <div class="anni-particles-bg" style="padding: 2rem 0;">
                                <a> </a>
                                <div class="anni-content">
                                    <h1 class="anni-main-title"><span style="color: #ff6b00;">17.&ordm; Aniversario</span><br>Precio M&aacute;s Bajo Del A&ntilde;o</h1>
                                    <a>
                                        <div class="anni-countdown-container">
                                            <div class="anni-countdown-title">Venta Flash Termina En:</div>
                                            <div class="anni-countdown-box">
                                                <div class="anni-countdown-item">
                                                    <div class="anni-countdown-value" id="anni-days">00</div>
                                                    <div class="anni-countdown-label">Days</div>
                                                </div>
                                                <div class="anni-countdown-item">
                                                    <div class="anni-countdown-value" id="anni-hours">00</div>
                                                    <div class="anni-countdown-label">Hours</div>
                                                </div>
                                                <div class="anni-countdown-item">
                                                    <div class="anni-countdown-value" id="anni-minutes">00</div>
                                                    <div class="anni-countdown-label">Minutes</div>
                                                </div>
                                                <div class="anni-countdown-item">
                                                    <div class="anni-countdown-value" id="anni-seconds">00</div>
                                                    <div class="anni-countdown-label">Seconds</div>
                                                </div>
                                            </div>
                                        </div>
                                    </a><a href="/pages/aniversario-2025-retekess-prime-day" target="&ldquo;_blank&rdquo;" class="anni-cta-btn">Compra Ahora</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>-->
            <div class="slide">
                <div class="blur-up lazyload">
                    <img class="blur-up lazyload desktop-hide sw-slide" data-src="{{static_path}}/assets/images/index/banner/banner-pc3.webp" src="{{static_path}}/assets/images/index/banner/banner-pc3.webp" alt="tour guide system" title="tour guide system" />
                    <img class="blur-up lazyload mobile-hide sw-slide" data-src="{{static_path}}/assets/images/index/banner/banner-mobile3.webp" src="{{static_path}}/assets/images/index/banner/banner-mobile3.webp" alt="tour guide system" title="tour guide system" />
                    <div class="slideshow__text-wrap slideshow__overlay left">
                        <div class="slideshow__text-content">
                            <div class="slide-content">
                                <h2 class="slide-title"><span class="tourindustry" style="color: #1f909e; font-size: 26px; font-weight: 500;">Tour&bull;F&aacute;brica&bull;Iglesia&bull;Excursi&oacute;n&bull;Escuela</span><br>SISTEMA DE GU&Iacute;A TUR&Iacute;STICA</h2>
                                <p class="slide-desc" style="color: #414141; font-weight: 300;">El sistema Retekess se usa en m&aacute;s de <span style="color: #1f909e; font-weight: 500;">6,800+</span> eventos globalmente, garantizando que todos escuchen al orador con claridad</p>
                                <a href="/collections/radioguias-sistema" class="slide-button">Compra Ahora</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="slide">
                <div class="blur-up lazyload">
                    <img class="blur-up lazyload desktop-hide sw-slide" data-src="{{static_path}}/assets/images/index/banner/banner-pc4.webp" src="{{static_path}}/assets/images/index/banner/banner-pc4.webp" alt="Long range pager system" title="Long range pager system" />
                    <img class="blur-up lazyload mobile-hide " data-src="{{static_path}}/assets/images/index/banner/banner-mobile4.webp" src="{{static_path}}/assets/images/index/banner/banner-mobile4.webp" alt="Long range pager system" title="Long range pager system" />
                    <div class="slideshow__text-wrap slideshow__overlay left">
                        <div class="slideshow__text-content">
                            <div class="slide-content">
                                <h2 class="slide-title"><span style="color: #1f909e;">SISTEMA DE BUSCAPERSONAS </span><br>DE LARGO ALCANCE</h2>
                                <p class="slide-desc" style="color: #414141; font-weight: 300;">Buscapersonas de largo alcance basado en FSK con amplia cobertura, dise&ntilde;ado para grandes restaurantes, salas de eventos y log&iacute;stica</p>
                                <a href="/collections/avisadores-de-clientes" class="slide-button">Compra Ahora</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!--End Home slider-->
<!--Store Feature-->
<div class="store-features section">
    <div class="container">
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3 col-lg-3 home-policy">
                <i class="an an-truck"></i>
                <h5>ENVÍO GRATIS</h5>
                <p class="sub-text">Envío Gratuito en España</p>
            </div>
            <div class="col-12 col-sm-6 col-md-3 col-lg-3 home-policy">
                <i class="an an-money-bill-wave"></i>
                <h5>CUPONES</h5>
                <p class="sub-text">Regístrate para Recibir Ofertas Exclusivas</p>
            </div>
            <div class="col-12 col-sm-6 col-md-3 col-lg-3 home-policy">
                <i class="an an-shield-alt"></i>
                <h5>30 DÍAS</h5>
                <p class="sub-text">Regresar sin Motivo</p>
            </div>
            <div class="col-12 col-sm-6 col-md-3 col-lg-3 home-policy">
                <i class="an an-headphones"></i>
                <h5>2 AÑOS</h5>
                <p class="sub-text">Política de Garantía del Anfitrión</p>
            </div>
        </div>
    </div>
</div>
<!--End Store Feature-->

<div class="pb-4" style="padding-top: 10px;">
    <div class="container" style="max-width: 1800px; background-image: linear-gradient(to top, #8f9095de, #fff, #b3b5bfb3); ">
        <div class="row">
            <div class="col-12 col-sm-12 col-md-3 col-lg-3" style="display: flex; justify-content: center;">
                <div style="padding: 14% 14% 8%;" class="d-lg-block d-none">
                    <h1 class="new-arrival-h1" style="font-size: 45px; font-weight: bold; margin-top: -30px;">Nuevo producto</h1>
                    <p class="new-arrival-p" style="color: #3d4482; font-size: 17px; font-weight: bold;">Los nuevos productos de la serie T130 est&aacute;n a la venta a un precio especial, &iexcl;c&oacute;mpralo ahora!</p>
                    <a href="/collections"><img class="new-arrival-img" style="width: 65%; margin-top: -10px;" src="{{static_path}}/assets/images/index/new-arriva.png"></a>
                </div>
                <div class="d-lg-none d-block">
                    <div style="padding: 20px; margin-top: 20px;">
                        <h1 style="font-size: 28px; font-weight: bold; margin-top: -15px;">Nuevo producto</h1>
                        <p style="color: #3d4482; font-size: 20px; font-weight: bold;">&iexcl;Compra ahora y deja que te traigan una nueva experiencia!</p>
                    </div>
                </div>
            </div>
            <div class="productSlider-style2 col-12 col-sm-12 col-md-9 col-lg-9" style="margin-top: 3%;">
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/t130p-t131p-sistema-de-audioguia" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/es-t130p.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/receptor-con-gancho-para-la-oreja-para-guias-turisticos-retekess-tt126r" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/retekess-tt126-tt126r-gancho-para-oreja-sistema-de-visitas-guiadas.webp" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/tt129-sistema-de-comunicacion-full-duplex-inalambrico" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/retekess-tt129-sistema-de-comunicacion-inalambrica-full-duplex-es.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/audifono-inteligente-con-reduccion-de-ruido-ric-retekess-te201" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/retekess-te201-audifono-es.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/tt127-equipos-audio-guias-radioguias" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/audioguia-retekess-tt127.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/retekess-td168r-sistema-de-llamadas-inalambrico" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/retekess-td168r.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-6 col-sm-6 col-md-4 col-lg-4 item">
                    <a href="/products/td175p-sistema-de-localizacion-para-restaurantes" tabindex="0">
                        <div class="grid-view_image" style="position: relative; overflow: hidden; border-radius: 10px;">
                            <img style="border-radius: 10px; width: 100%; transition: transform 0.3s ease;" src="{{static_path}}/assets/images/index/newArrival/retekess-td175p.jpg" alt="whisper-system-t130p-t131p-tour-guide-system-retekess">
                            <div class="hover-effect" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(31, 144, 158, 0); transition: all 0.3s ease;">
                                <button class="shop-btn" style="background: #1f909e; color: white; border: none; padding: 12px 30px; border-radius: 4px; font-weight: 600; font-size: 16px; cursor: pointer; opacity: 0; transform: translateY(20px); transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);" tabindex="0"> Shop Now </button>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>












<div style="background-color: #f1f3f5;">
    <div class="container section" style="max-width: 1800px;">
        <div class="related-product grid-products">
            <div class="section-header">
                <h2 class="section-header__title text-center mb-2"><span>Categorías de Inalámbrico Productos & Productos Recomendados</span></h2>
                <div class="row">
                    <div class="col-8 text-left">
                        <h3 style="font-size: 26px; position: relative;">
                            <a href="/collections/radioguias-sistema">Guía Turística Sistema</a>
                            <!--<span style="background: linear-gradient(135deg, #ff6b00 0%, #ff6b00 100%); color: white; font-size: 12px; font-weight: bold; padding: 4px 12px; border-radius: 15px; margin-left: 0px; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); position: relative; top: -4px; text-transform: uppercase;">
                                    <a href="/shop" target="_blank" rel="noopener">FREE DEMO KIT</a>
                            </span>-->
                        </h3>
                    </div>
                    <div class="col-4 text-right"><div><a style="font-weight: bold; color: #000;" href="/collections/radioguias-sistema">Ver Más &gt;</a></div></div>
                </div>
            </div>
            <!-- 动态循环 - Guía Turística Sistema -->
            <div class="row">
                {% if Model.HomeProducts.TourGuideProducts and Model.HomeProducts.TourGuideProducts.size > 0 %}
                    {% for product in Model.HomeProducts.TourGuideProducts %}
                        <div class="col-6 col-sm-6 col-md-4 col-lg-2 item">
                            <div class="p-4 bg-white">
                                <!-- start product image -->
                                <div class="product-image">
                                    <!-- start product image -->
                                    <a href="{{ product.ProductUrl }}" class="product-img">
                                        <!-- image -->
                                        <img class="primary blur-up lazyload" data-src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        <!-- End image -->
                                        <!-- Hover image -->
                                        {% if product.HoverImage %}
                                            <img class="hover blur-up lazyload" data-src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        {% endif %}
                                        <!-- End hover image -->
                                    </a>
                                    <!-- end product image -->
                                    <!--Product label-->
                                    {% if product.IsHot or product.IsNew %}
                                        <div class="product-labels">
                                            {% if product.IsHot %}<span class="lbl pr-label2">Hot</span>{% endif %}
                                            {% if product.IsNew %}<span class="lbl pr-label1">New</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    <!--Product label-->
                                    <!--Product Button-->
                                    <div class="button-set style1">
                                        <ul>
                                            <li>
                                                <!--Cart Button-->
                                                <a href="{{ product.ProductUrl }}" title="{{ "products.goods.addToCart" | translate }}"
                                                   class="btn-icon btn-addto-cart"
                                                   data-product-id="{{ product.ProductId }}">
                                                    <i class="icon an an-shopping-cart"></i>
                                                    <span class="tooltip-label">{{ "products.goods.addToCart" | translate }}</span>
                                                </a>
                                                <!--End Cart Button-->
                                            </li>
                                            <li>
                                                <!--Wishlist Button-->
                                                <div class="wishlist-btn">
                                                    <a class="btn-icon wishlist add-to-wishlist" href="javascript:void(0);"
                                                       data-product-id="{{ product.ProductId }}"
                                                       data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                        {% if product.IsFavorited %}
                                                            <i class="icon an an-heart"></i>
                                                        {% else %}
                                                            <i class="icon an an-heart-o"></i>
                                                        {% endif %}
                                                        <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                                    </a>
                                                </div>
                                                <!--End Wishlist Button-->
                                            </li>
                                        </ul>
                                    </div>
                                    <!--End Product Button-->
                                </div>
                                <!-- end product image -->
                                <!--start product details -->
                                <div class="product-details text-left">
                                    <!-- product name -->
                                    <div class="product-name">
                                        <a href="{{ product.ProductUrl }}">{{ product.ProductName }}</a>
                                    </div>
                                    <!-- End product name -->
                                    <!-- product price -->
                                    <div class="product-price">
                                        <span class="price">{{ product.PriceFormat }}</span>
                                        {% if product.OldPriceFormat and product.OldPriceFormat != "" %}
                                            <span class="old-price">{{ product.OldPriceFormat }}</span>
                                        {% endif %}
                                    </div>
                                    <!-- End product price -->
                                </div>
                                <!-- End product details -->
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}

            </div>
            <div class="row mt-4 mb-2">
                <div class="col-8 text-left"><h3 style="font-size: 26px; position: relative;"><a href="/collections/avisadores-de-clientes">Sistema de Buscapersonas</a> </h3></div>
                <div class="col-4 text-right"><div><a style="font-weight: bold; color: #000;" href="/collections/avisadores-de-clientes">Ver Más &gt;</a></div></div>
            </div>
            <!-- 动态循环 - Sistema de Buscapersonas -->
            <div class="row">
                {% if Model.HomeProducts.PagingSystemProducts and Model.HomeProducts.PagingSystemProducts.size > 0 %}
                    {% for product in Model.HomeProducts.PagingSystemProducts %}
                        <div class="col-6 col-sm-6 col-md-4 col-lg-2 item">
                            <div class="p-4 bg-white">
                                <!-- start product image -->
                                <div class="product-image">
                                    <!-- start product image -->
                                    <a href="{{ product.ProductUrl }}" class="product-img">
                                        <!-- image -->
                                        <img class="primary blur-up lazyload" data-src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        <!-- End image -->
                                        <!-- Hover image -->
                                        {% if product.HoverImage %}
                                            <img class="hover blur-up lazyload" data-src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        {% endif %}
                                        <!-- End hover image -->
                                    </a>
                                    <!-- end product image -->
                                    <!--Product label-->
                                    {% if product.IsHot or product.IsNew %}
                                        <div class="product-labels">
                                            {% if product.IsHot %}<span class="lbl pr-label2">Hot</span>{% endif %}
                                            {% if product.IsNew %}<span class="lbl pr-label1">New</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    <!--Product label-->
                                    <!--Product Button-->
                                    <div class="button-set style1">
                                        <ul>
                                            <li>
                                                <!--Cart Button-->
                                                <a href="{{ product.ProductUrl }}" title="{{ "products.goods.addToCart" | translate }}"
                                                   class="btn-icon btn-addto-cart"
                                                   data-product-id="{{ product.ProductId }}">
                                                    <i class="icon an an-shopping-cart"></i>
                                                    <span class="tooltip-label">{{ "products.goods.addToCart" | translate }}</span>
                                                </a>
                                                <!--End Cart Button-->
                                            </li>
                                            <li>
                                                <!--Wishlist Button-->
                                                <div class="wishlist-btn">
                                                    <a class="btn-icon wishlist add-to-wishlist" href="javascript:void(0);"
                                                       data-product-id="{{ product.ProductId }}"
                                                       data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                        {% if product.IsFavorited %}
                                                            <i class="icon an an-heart"></i>
                                                        {% else %}
                                                            <i class="icon an an-heart-o"></i>
                                                        {% endif %}
                                                        <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                                    </a>
                                                </div>
                                                <!--End Wishlist Button-->
                                            </li>
                                        </ul>
                                    </div>
                                    <!--End Product Button-->
                                </div>
                                <!-- end product image -->
                                <!--start product details -->
                                <div class="product-details text-left">
                                    <!-- product name -->
                                    <div class="product-name">
                                        <a href="{{ product.ProductUrl }}">{{ product.ProductName }}</a>
                                    </div>
                                    <!-- End product name -->
                                    <!-- product price -->
                                    <div class="product-price">
                                        <span class="price">{{ product.PriceFormat }}</span>
                                        {% if product.OldPriceFormat and product.OldPriceFormat != "" %}
                                            <span class="old-price">{{ product.OldPriceFormat }}</span>
                                        {% endif %}
                                    </div>
                                    <!-- End product price -->
                                </div>
                                <!-- End product details -->
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
            <div class="row mt-4 mb-2">
                <div class="col-8 text-left"><h3 style="font-size: 26px; position: relative;"><a href="/collections/avisadores-de-camareros">Presione para Servicio</a> </h3></div>
                <div class="col-4 text-right"><div><a style="font-weight: bold; color: #000;" href="/collections/avisadores-de-camareros">Ver Más &gt;</a></div></div>
            </div>
            <!-- 动态循环 - Presione para Servicio -->
            <div class="row">
                {% if Model.HomeProducts.CallSystemProducts and Model.HomeProducts.CallSystemProducts.size > 0 %}
                    {% for product in Model.HomeProducts.CallSystemProducts %}
                        <div class="col-6 col-sm-6 col-md-4 col-lg-2 item">
                            <div class="p-4 bg-white">
                                <!-- start product image -->
                                <div class="product-image">
                                    <!-- start product image -->
                                    <a href="{{ product.ProductUrl }}" class="product-img">
                                        <!-- image -->
                                        <img class="primary blur-up lazyload" data-src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" src="{% if product.PrimaryImage %}{{ product.PrimaryImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227{% else %}{{static_path}}/assets/images/product-images/elt-p-10.jpg{% endif %}" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        <!-- End image -->
                                        <!-- Hover image -->
                                        {% if product.HoverImage %}
                                            <img class="hover blur-up lazyload" data-src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" src="{{ product.HoverImage }}?x-oss-process=image/format,webp/resize,m_lfit,h_227,w_227" alt="{{ product.ProductName }}" title="{{ product.ProductName }}">
                                        {% endif %}
                                        <!-- End hover image -->
                                    </a>
                                    <!-- end product image -->
                                    <!--Product label-->
                                    {% if product.IsHot or product.IsNew %}
                                        <div class="product-labels">
                                            {% if product.IsHot %}<span class="lbl pr-label2">Hot</span>{% endif %}
                                            {% if product.IsNew %}<span class="lbl pr-label1">New</span>{% endif %}
                                        </div>
                                    {% endif %}
                                    <!--Product label-->
                                    <!--Product Button-->
                                    <div class="button-set style1">
                                        <ul>
                                            <li>
                                                <!--Cart Button-->
                                                <a href="{{ product.ProductUrl }}" title="{{ "products.goods.addToCart" | translate }}"
                                                   class="btn-icon btn-addto-cart"
                                                   data-product-id="{{ product.ProductId }}">
                                                    <i class="icon an an-shopping-cart"></i>
                                                    <span class="tooltip-label">{{ "products.goods.addToCart" | translate }}</span>
                                                </a>
                                                <!--End Cart Button-->
                                            </li>
                                            <li>
                                                <!--Wishlist Button-->
                                                <div class="wishlist-btn">
                                                    <a class="btn-icon wishlist add-to-wishlist" href="javascript:void(0);"
                                                       data-product-id="{{ product.ProductId }}"
                                                       data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                                        {% if product.IsFavorited %}
                                                            <i class="icon an an-heart"></i>
                                                        {% else %}
                                                            <i class="icon an an-heart-o"></i>
                                                        {% endif %}
                                                        <span class="tooltip-label">{{ "products.goods.addToFavorites"|translate}}</span>
                                                    </a>
                                                </div>
                                                <!--End Wishlist Button-->
                                            </li>
                                        </ul>
                                    </div>
                                    <!--End Product Button-->
                                </div>
                                <!-- end product image -->
                                <!--start product details -->
                                <div class="product-details text-left">
                                    <!-- product name -->
                                    <div class="product-name">
                                        <a href="{{ product.ProductUrl }}">{{ product.ProductName }}</a>
                                    </div>
                                    <!-- End product name -->
                                    <!-- product price -->
                                    <div class="product-price">
                                        <span class="price">{{ product.PriceFormat }}</span>
                                        {% if product.OldPriceFormat and product.OldPriceFormat != "" %}
                                            <span class="old-price">{{ product.OldPriceFormat }}</span>
                                        {% endif %}
                                    </div>
                                    <!-- End product price -->
                                </div>
                                <!-- End product details -->
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</div>









<div style="background-color: #f4f7fc; ">
    <div class="container" style="max-width: 1800px;">
        <!--联系我们模块-->
        <div class="about-us d-lg-block d-none" style="padding: 30px 0px;">
            <div style="padding: 0 5%;">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4" style="display: flex; align-items: center; flex-wrap: wrap; border-right: 1px solid #e5e5e5; flex-direction: column; margin-top: 30px;">
                        <p style="font-size: 20px; font-weight: bold; margin-bottom: 20px; width: 100%;">¿Preguntas?</p>
                        <p style="font-size: 20px; font-weight: bold; width: 100%;">No dude en ponerse en contacto con nosotros.</p>
                    </div>
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4" style="border-right: 1px solid #e5e5e5;">
                        <img src="{{static_path}}/assets/images/index/contacter-les-ventes.png" style="margin-bottom: 5px;">
                        <p style="font-weight: bold;"><a class="emaillink" href="/pages/contactus"> Contacto de ventas </a></p>
                        <p>El equipo comercial y técnico le proporcionará el asesoramiento más preciso en el contexto de la situación real.</p>
                    </div>
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <img src="{{static_path}}/assets/images/index/contact-par-ourriel.png" style="margin-bottom: 5px;">
                        <p style="font-weight: bold;"><a href="mailto:<EMAIL>"> Contacto por correo electrónico </a></p>
                        <p>Responderemos a su correo electrónico lo antes posible y estaremos encantados de ponernos en contacto con usted.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="about-us d-lg-none d-block" style="padding-left: 30px; padding-right: 30px;">
            <div class="">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4" style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center; border-bottom: 1px solid #e5e5e5; margin-bottom: 20px;">
                        <p style="font-size: 16px; font-weight: bold;">¿Preguntas?</p>
                        <p style="font-size: 16px; font-weight: bold;">No dude en ponerse en contacto con nosotros.</p>
                    </div>
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <div class="row">
                            <div style="display: flex; align-items: center;" class="col-2 col-sm-2 col-md-2 col-lg-2"><img src="{{static_path}}/assets/images/index/contacter-les-ventes.png" style="margin-bottom: 5px;"></div>
                            <div class="col-10 col-sm-10 col-md-10 col-lg-10">
                                <p style="font-weight: bold; font-size: 12px;"><a class="emaillink" href="/pages/contactus"> Contacto de ventas </a></p>
                                <p style="font-size: 12px;">El equipo comercial y técnico le proporcionará el asesoramiento más preciso en el contexto de la situación real.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                        <div class="row">
                            <div style="display: flex; align-items: center;" class="col-2 col-sm-2 col-md-2 col-lg-2"><img src="{{static_path}}/assets/images/index/contact-par-ourriel.png" style="margin-bottom: 5px;"></div>
                            <div class="col-10 col-sm-10 col-md-10 col-lg-10">
                                <p style="font-weight: bold; font-size: 12px;"><a href="mailto:<EMAIL>"> Contacto por correo electrónico </a></p>
                                <p style="font-size: 12px;">Responderemos a su correo electrónico lo antes posible y estaremos encantados de ponernos en contacto con usted.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--联系我们模块-->
    </div>
</div>
<div class="container section" style="max-width: 1800px;">
    <div class="section-header">
        <h2 class="section-header__title text-center mb-2"><span>Inalámbrico Soluciones para Diversas Industrias</span></h2>
    </div>
    <div class="grid-products wireless-solutions">
        <div class="productSlider-style2">
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/restaurante-paginacion-de-invitados" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/1retekess-pager-systems-for-restaurant.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Restaurante</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/seguro-medico" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/2retekess-nurse-call-system-for-medical-and-healthcare.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Medicina y Salud</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/al-aire-ibre" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/3retekess-tour-guide-systems-for-toursim.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Turismo</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/hajj" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/4hajj-and-umrah.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Hayy y Umrah</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/museo" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/5wireless-tour-guide-system-museum.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Museo</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/guarderia-de-la-iglesia" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/churchnursery2.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Guardería de la iglesia</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/fabricas-y-sitios-industriales" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/audio-tour-guide-device-for-factory-tour2.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Visita de fábrica</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/iglesia" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-traduccion-de-la-Iglesia-y-la-corte.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Traducción De La Iglesia Y La Corte</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/entrenamiento-y-educacion" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/7retekess-tour-guide-system-for-training-and-education.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Entrenamiento Y Educación</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/restaurante-paginacion-de-servicio" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-restaurante-paginacion-de-servicio.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Restaurante Paginación De Servicio</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/hoteles-complejos-turisticos" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-hotel-y-resort.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Hotel Y Resort</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/tiendas-minoristas-comestibles" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/6retekess-wireless-calling-system-for-retail-and-grocery-store.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Tienda Minorista Y De Abarrotes</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/spa-y-salon" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-balneario-y-salon.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Balneario Y Salón</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/almacen" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-deposito.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Depósito</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="item">
                <div class="grid-view_image">
                    <a href="/pages/solucion/casino" tabindex="0">
                        <div class="btn07">
                            <img src="{{static_path}}/assets/images/index/retekess-casino.jpg" style="border-radius: 20px;">
                            <div style="position: absolute; bottom: 0; background-color: rgba(58, 126, 156, 0.7); border-radius: 0 0 20px 20px; width: 100%; height: 50px; display: flex; justify-content: center; align-items: center;">
                                <h2 class="wireless-solutions-h2">Casino</h2>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="container section" style="max-width: 1800px;background-color: #fff;">
    <div class="section-header">
        <h2 class="section-header__title text-center mb-2"><span>Los Compañeros Eligen Juntos</span></h2>
    </div>
    <div class=" grid-products">
        <div class="productSlider-style2">
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/2.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/1.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/3.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/4.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/cliente-cooperativa-escuela-retekess.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/retekess-radioguias.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/sistema-de-agencia-de-viajes-retekess.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/sistema-de-guia-turistica-retekess.jpeg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/certificacion-de-guia-turistico-retekess.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/7.jpg" style="border-radius: 10px;"></div>
            </div>
            <div class="item">
                <div class="grid-view_image"><img src="{{static_path}}/assets/images/index/retekess-tourisme6.jpg" style="border-radius: 10px;"></div>
            </div>
        </div>
    </div>
</div>
<div class="content-bg mb-4 py-3">
    <div class="container" style="max-width: 1800px;">
        <h2 class="d-lg-block d-none" style="font-size: 36px; font-weight: bold; text-align: center;">Servicios Personalizados</h2>
        <h2 class="d-lg-none d-block" style="font-size: 16px; font-weight: bold; text-align: center;">Servicios Personalizados</h2>
        <div class="row">
            <div style="background-color: #fff; padding: 10px;" class="col-12 col-sm-12 col-md-4 col-lg-4">
                <div style="border: 1px solid #dee2e6; padding: 20px;">
                    <a href="#"><img src="{{static_path}}/assets/images/index/produits-personnalises.png"></a>
                    <p style="font-weight: bold; margin-top: 20px;"><a href="#">Personalizar El Producto</a></p>
                    <p>Contamos con un equipo de desarrollo profesional para satisfacer sus necesidades personalizadas, como el color del producto, el logotipo, la función, el idioma, el embalaje, etc.</p>
                </div>
            </div>
            <div style="background-color: #fff; padding: 10px;" class="col-12 col-sm-12 col-md-4 col-lg-4">
                <div style="border: 1px solid #dee2e6; padding: 20px;">
                    <a href="#"> <img src="{{static_path}}/assets/images/index/solutions-de-communication-personnalisees.png"></a>
                    <p style="font-weight: bold; margin-top: 20px;"><a href="#">Soluciones De Comunicación Personalizadas</a></p>
                    <p>En combinación con las características de cada industria, proporcionamos a las empresas soluciones de comunicación exclusivas para mejorar la eficiencia y aumentar los beneficios.</p>
                </div>
            </div>
            <div style="background-color: #fff; padding: 10px;" class="col-12 col-sm-12 col-md-4 col-lg-4">
                <div style="border: 1px solid #dee2e6; padding: 20px;">
                    <a href="#"><img src="{{static_path}}/assets/images/index/quantitedemballages-personnalises.png"></a>
                    <p style="font-weight: bold; margin-top: 20px;"><a href="#">Personalizar La Cantidad Del Paquete</a></p>
                    <p>Trabajar directamente con Retekess ofrece una gran flexibilidad y le proporcionaremos la cantidad de paquetes que necesite en función del tamaño de su empresa.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!--<div class="d-none d-lg-block slideshow slideshow-wrapper pb-4">
    <div>
        <img class="w-100 d-none d-md-block" src="{{static_path}}/assets/images/index/1900x600-aboutus-us-pc-retekess.jpg" alt="aboutus"> <img class="w-100 d-md-none" src="{{static_path}}/assets/images/index/1900x600-aboutus-us-pc-retekess.jpg" alt="aboutus">
        <div class="about-us-num-pc">
            <div style="flex: 1;">
                <h2 class="new-h2" style="font-weight: bold;">16</h2>
                <p class="new-p1">Years+</p>
                <p class="new-p2">16+ Years of <br>Experience</p>
            </div>
            <div style="flex: 1; border-left: 1px solid #888; border-right: 1px solid #888;">
                <h2 class="new-h2" style="font-weight: bold;">146</h2>
                <p class="new-p1">Countries+</p>
                <p class="new-p2">Available in 146+<br>Countries</p>
            </div>
            <div style="flex: 1;">
                <h2 class="new-h2" style="font-weight: bold;">90000</h2>
                <p class="new-p1">Products+</p>
                <p class="new-p2">90000+ Products <br>Sold Worldwide</p>
            </div>
        </div>
        <div class="aboutus-con-pc">Our mission is to make communication easier in various fields, so that every company has a effortless, seamless and affordable wireless communication solution. Retekess provides our customers with the professional technical support, practical solutions and best quality products as well as considerate services.</div>
    </div>
</div>-->
<!-- 手机 -->
<!--<div class="d-block d-lg-none slideshow slideshow-wrapper pb-4">
    <div><img class="w-100 d-none d-md-block" src="{{static_path}}/assets/images/index/755x1100-aboutus-us-mobile-retekess.jpg" alt="aboutus"> <img class="w-100 d-md-none" src="{{static_path}}/assets/images/index/755x1100-aboutus-us-mobile-retekess.jpg" alt="aboutus">-->
<!-- </a> -->
<!--</div>
    <div class="about-us-num-m">
        <div style="flex: 1;">
            <h1 class="new-h1-m" style="font-weight: bold;">16</h1>
            <h2 class="new-h2-m" style="font-weight: bold;">Years+</h2>
            <p class="new-p2-m" style="font-weight: bold;">16+ Years of <br>Experience</p>
        </div>
        <div class="mt-3 mb-3 pt-2 pb-2" style="flex: 1; border-top: 1px solid #888; border-bottom: 1px solid #888;">
            <h1 class="new-h1-m" style="font-weight: bold;">146</h1>
            <h2 class="new-h2-m" style="font-weight: bold;">Countries</h2>
            <p class="new-p2-m" style="font-weight: bold;">Available in 146+ <br>Countries</p>
        </div>
        <div style="flex: 1;">
            <h1 class="new-h1-m" style="font-weight: bold;">90000</h1>
            <h2 class="new-h2-m" style="font-weight: bold;">Products+</h2>
            <p class="new-p2-m" style="font-weight: bold;">90000+ Products <br>Sold Worldwide</p>
        </div>
    </div>
    <div class="aboutus-con-m">Our vision is to make communication easier in every field, so that every company has a simple, complete and affordable wireless communication solution. We offer the best technical support and solutions to provide the best quality products and services to every customer.</div>
</div>
<div class="container home-brick-box py-4" style="background-color: #fff;max-width: 1800px;">
    <div class="section tab-slider-product">
        <h2 class="brick-title">Trusted by Over 150K Businesses</h2>
        <div class="section-header">
            <div class="section-header-right text-center">
                <ul class="tabs nav nav-tabs head-font d-inline" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="active" id="featured-tab" data-bs-toggle="tab" data-bs-target="#featured" role="tab" aria-controls="featured" aria-selected="true">RESTAURANTS</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a id="bestseller-tab" data-bs-toggle="tab" data-bs-target="#bestseller" role="tab" aria-controls="bestseller" aria-selected="false">TOURISM</a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a id="mostviewd-tab" data-bs-toggle="tab" data-bs-target="#mostviewd" role="tab" aria-controls="mostviewd" aria-selected="false">OTHERS</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="tab-content" id="productTabsContent">
            <div class="tab-pane show active grid-products" id="featured" role="tabpanel" aria-labelledby="featured-tab">
                <img src="{{static_path}}/assets/images/index/retekess-paging-system-for-restaurants.jpg" alt="Retekess restaurants paging system trusted by business">
            </div>
            <div class="tab-pane show grid-products" id="bestseller" role="tabpanel" aria-labelledby="bestseller-tab">
                <img src="{{static_path}}/assets/images/index/retekess-tour-guide-systems-for-travel-agencies.jpg" alt="Retekess wireless tour guide system trusted by business">
            </div>
            <div class="tab-pane show grid-products" id="mostviewd" role="tabpanel" aria-labelledby="mostviewd-tab">
                <img src="{{static_path}}/assets/images/index/retekess-wireless-products-for-factory-and-more.jpg" alt="Retekess wireless products trusted by business">
            </div>
        </div>
    </div>
</div>-->
<div class="container section" style="max-width: 1800px;background-color: #fff;">
    <div class="section-header">
        <h2 class="section-header__title text-center mb-2"><span>Blog</span></h2>
    </div>
    <div class="collection-slider latest-blog">
        <div class="collection-slider-4items">
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/sistema-de-microfono-para-guia-turistico" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog1.jpg" alt="¿Qué es el Sistema de Micrófono para Guías Turísticos?" title="¿Qué es el Sistema de Micrófono para Guías Turísticos?" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/sistema-de-microfono-para-guia-turistico" tabindex="0">¿Qué es el Sistema de Micrófono para Guías Turísticos?</a>
                            </h2>
                            <span class="article__date">Apr 4, 2023</span>
                            <div class="article__grid-excerpt">
                                El sistema de micrófonos para guías turísticos es un equipo de comunicación inalámbrica que se utiliza en visitas guiadas, presentaciones y otras visitas públicas. Consta de un transmisor que lleva el guía y varios receptores que se entregan a los turistas u oyentes. El transmisor envía las señales de audio del micrófono del guía directamente a los receptores, lo que permite a los oyentes escuchar claramente la voz del guía sin interferencias de ruidos o distancias. Esta tecnología es beneficiosa para grupos grandes, entornos ruidosos o para personas con deficiencias auditivas, ya que garantiza que el mensaje sea claro y audible.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/sistema-de-microfono-para-guia-turistico" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/guia-turistica-microfono-y-auriculares" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog2.jpg" alt="El Mejor Micrófono y Auriculares para Guía Turístico" title="El Mejor Micrófono y Auriculares para Guía Turístico" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/guia-turistica-microfono-y-auriculares" tabindex="0">El Mejor Micrófono y Auriculares para Guía Turístico</a>
                            </h2>
                            <span class="article__date">Nov 29, 2023</span>
                            <div class="article__grid-excerpt">
                                Cuando se trata de visitas guiadas y eventos de oratoria, no se puede subestimar la importancia de una comunicación clara. Aquí es donde entra en juego el sistema de guía turística inalámbrico. Estos dispositivos permiten a los guías turísticos hablar con grandes grupos de personas de forma clara y eficaz, garantizando que todos escuchen y comprendan el mensaje que se transmite.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/guia-turistica-microfono-y-auriculares" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/sistema-de-guia-turistico-whisper" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog3.jpg" alt="El Sistema De Guía Turística Inalámbrico Whisper Puede Reemplazar Los Parlantes" title="El Sistema De Guía Turística Inalámbrico Whisper Puede Reemplazar Los Parlantes" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/sistema-de-guia-turistico-whisper" tabindex="0">El Sistema De Guía Turística Inalámbrico Whisper Puede Reemplazar Los Parlantes</a>
                            </h2>
                            <span class="article__date">Aug 4, 2023</span>
                            <div class="article__grid-excerpt">
                                En la industria del turismo, la comunicación efectiva juega un papel clave para brindar a los viajeros experiencias memorables e inmersivas. Tradicionalmente, los altavoces se han utilizado para transmitir información durante los viajes. Sin embargo, con el avance de la tecnología, el sistema de guía turístico inalámbrico se ha convertido en una mejor opción.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/sistema-de-guia-turistico-whisper" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/preguntas-frecuentes-sobre-el-sistema-de-localizacion-td163" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog4.jpg" alt="Preguntas frecuentes sobre el sistema de localización TD163" title="Preguntas frecuentes sobre el sistema de localización TD163" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/preguntas-frecuentes-sobre-el-sistema-de-localizacion-td163" tabindex="0">Preguntas frecuentes sobre el sistema de localización TD163</a>
                            </h2>
                            <span class="article__date">Apr 21, 2023</span>
                            <div class="article__grid-excerpt">
                                Hemos ordenado las preguntas y respuestas más frecuentes sobre el sistema de buscapersonas TD163 para que sea más fácil para todos saber cómo usar el sistema de buscapersonas TD163.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/preguntas-frecuentes-sobre-el-sistema-de-localizacion-td163" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/consideracion-de-factores-para-organizar-visitas-a-fabricas-una-guia-completa" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog5.jpg" alt="Consideración de factores para organizar visitas a fábricas: una guía completa" title="Consideración de factores para organizar visitas a fábricas: una guía completa" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/consideracion-de-factores-para-organizar-visitas-a-fabricas-una-guia-completa" tabindex="0">Consideración de factores para organizar visitas a fábricas: una guía completa</a>
                            </h2>
                            <span class="article__date">Dec 13, 2023</span>
                            <div class="article__grid-excerpt">
                                Las visitas a fábricas ofrecen una oportunidad única para que individuos y grupos obtengan información sobre el funcionamiento interno de las instalaciones industriales. Ya seas un profesional de la industria, un estudiante o simplemente tengas curiosidad por saber cómo se fabrican los productos, organizar una visita a una fábrica puede ser una experiencia enriquecedora. Sin embargo, para garantizar una visita exitosa e informativa, es fundamental considerar varios factores de antemano. En esta publicación de blog, exploraremos las consideraciones clave para organizar visitas a fábricas y brindaremos información valiosa sobre cómo aprovechar al máximo estas experiencias.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/consideracion-de-factores-para-organizar-visitas-a-fabricas-una-guia-completa" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/equipo-de-traduccion" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog6.jpg" alt="¿Por Qué Necesita Equipo de Traducción para su Evento?" title="¿Por Qué Necesita Equipo de Traducción para su Evento?" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/equipo-de-traduccion" tabindex="0">¿Por Qué Necesita Equipo de Traducción para su Evento?</a>
                            </h2>
                            <span class="article__date">Jun 04,2025</span>
                            <div class="article__grid-excerpt">
                                El equipo de traducción consta de algunos transmisores y receptores. El hablante activo habla con el transmisor, y los traductores reciben sus mensajes de los receptores, mientras traducen estos mensajes a un idioma que la audiencia pueda entender y luego los transmiten a la audiencia. Tal vez vea esto y aún no sepa mucho sobre cómo funciona el equipo de traducción, entonces puede consultar la figura a continuación.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/equipo-de-traduccion" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="collection-grid-item">
                <div class="wrap-blog">
                    <a href="/blog/sistema-de-audio-para-guia-turistico-retekess" class="article__grid-image" tabindex="0">
                        <img src="{{static_path}}/assets/images/index/blog/blog7.jpg" alt="Lo Que Dijo un Guía Español Sobre el Sistema de Audio para Guías Turísticos Retekess" title="Lo Que Dijo un Guía Español Sobre el Sistema de Audio para Guías Turísticos Retekess" class="blur-up lazyloaded">
                    </a>
                    <div class="article__grid-meta article__grid-meta--has-image">
                        <div class="wrap-blog-inner">
                            <h2 class="h3 article__title text-capitalize">
                                <a href="/blog/sistema-de-audio-para-guia-turistico-retekess" tabindex="0">Lo Que Dijo un Guía Español Sobre el Sistema de Audio para Guías Turísticos Retekess</a>
                            </h2>
                            <span class="article__date">Sep 5, 2022</span>
                            <div class="article__grid-excerpt">
                                Ricardo Domínguez, guía turístico oficial y periodista local en Madrid, compró hace dos meses el sistema de audio para guías turísticos Retekess TT122 y lo utilizó para dirigir visitas al Museo del Prado, Reina Sofía, Palacio Real, Thyssen y El Escorial.
                            </div>
                            <ul class="list--inline article__meta-buttons">
                                <li><a href="/blog/sistema-de-audio-para-guia-turistico-retekess" class="text-capitalize" tabindex="0">Leer Más</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--<div class="container section" style="max-width: 1800px;background-color: #fff;">
    <div class="section-header">
        <h2 class="section-header__title text-center mb-2"><span>Testimonials from Our Partners</span></h2>
    </div>
    <div class="blog-post-slider">
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">- Rob Stinson</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Jason Murray</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Terry Thompson</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-David B.</p>
        </div>
        <div class="blogpost-item text-center">
            <p class="post-excerpt text-left more">In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non,In mattis scelerisque magna, ut tincidunt ex. Quisque nibh urna, pretium in tristique in, bibendum sed libero. Pellentesque mauris nunc, pretium non erat non</p>
            <a class="LoadMore" style="color: var(--theme-color);">Read More</a>
            <p class="post-excerpt text-right">-Steven Vesely</p>
        </div>
    </div>
</div>-->
<!-- Start Addtocart Added Popup -->
<div class="modal fade" id="pro-addtocart-popup" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                        <div class="addtocart-inner text-center clearfix">
                            <h4 class="title">Added to your shopping cart successfully.</h4>
                            <div class="pro-img mb-2">
                                <img class="img-fluid blur-up lazyload" src="{{static_path}}/assets/images/product-images/addtocart-popup-img.jpg" data-src="{{static_path}}/assets/images/product-images/addtocart-popup-img.jpg" alt="Added to your shopping cart successfully." title="Added to your shopping cart successfully." />
                            </div>
                            <div class="pro-details">
                                <p class="pro-name mb-1">Mobile Galaxy S6 Edge</p>
                                <p class="sku mb-0">Color: Gray</p>
                                <p class="mb-0 qty-total">1 X $113.88</p>
                                <div class="addcart-total mt-3 mb-3">
                                    Total: <b class="price">$113.88</b>
                                </div>
                                <div class="button-action">
                                    <a href="checkout-style1.html" class="btn btn-primary view-cart mx-1">Go To Checkout</a>
                                    <a href="index.html" class="btn btn-secondary">Continue Shopping</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Addtocart Added Popup -->
<!--Newsletter Popup-->
<div id="newsletter-modal-fade" class="mfp-bg mfp-zoom-in mfp-ready mfp-hide"></div>
<div id="newsletter-modal" class="style3 mfp-with-anim mfp-hide">
    <div class="newsltr-tbl row" style="background-image: url({{static_path}}/assets/images/newsletter/newsletter-red-bg.jpg); background-size: cover; background-repeat: no-repeat; border-radius: 10px; ">
        <div class="col-12">
            <div class="newsltr-text text-center coupon_info_box">
                <h2 class="coupon_info_title">Congratulate</h2>
                <p class="coupon_info_content">Enjoy saving money with a great coupn offer! </p>
                <div class="coupon_info_discounted text-center">
                    <div class="coupon_discount">For orders over $1,000.00</div>
                    <div class="coupon_price notranslate"><span class="price USD"><span class="symbols">$</span>100.00</span></div>
                    <div class="coupon_date">Expires 10 days after successful redemption</div>
                </div>
                <div class="coupon_info_btn operation_click " data-code="LQLZIISB" style="background-color:#000000; color:#ffffff">Get it</div>
            </div>
        </div>
    </div>
    <button title="Close (Esc)" type="button" class="mfp-close">×</button>
</div>
<!--End Newsletter Popup-->
<style>
    .wireless-solutions .wireless-solutions-h2 {
        color: black;
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 0;
    }

    .home-slideshow .slide-content {
        /*        position: absolute;
        top: 22%;
        bottom: 22%;
        left: 8.6%;*/
        width: 670px;
        color: #fff;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: all 0.3s ease;
        text-align: left;
    }

    .slide-title {
        color: #000;
        font-size: clamp(32px, 4vw, 56px);
        font-weight: 700;
        line-height: 1.2;
        margin: 0;
        padding-top: 20px;
    }

    .slide-desc {
        font-size: clamp(16px, 1.8vw, 26px);
        line-height: 1.4;
        margin: 0 0 2em 0;
    }

    .slide-button {
        display: inline-block;
        padding: 12px 40px;
        background: #FF6B00;
        color: #fff;
        font-size: clamp(14px, 1.5vw, 20px);
        font-weight: 700;
        border-radius: 30px;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;
        width: fit-content;
    }

    .section-header h2 {
        font-size: 34px;
        font-weight: 700;
        line-height: 1.3;
        text-transform: capitalize;
        margin: 0 auto;
    }

    .grid-view_image {
        position: relative;
        overflow: hidden;
        margin: 0 auto 15px;
    }

    .content-bg {
        background: #fafafa;
    }

    .about-us-num-pc {
        position: absolute;
        top: 30%;
        left: 15%;
        text-align: center;
        width: 70%;
        display: flex;
    }

    .new-h2 {
        font-size: 40px;
        margin-bottom: 1.5rem;
    }

    .new-p1 {
        font-weight: bold;
        font-size: 20px;
    }

    .new-p2 {
        font-size: 18px;
    }

    .aboutus-con-pc {
        position: absolute;
        width: 56%;
        left: 22%;
        top: 70%;
        text-align: center;
        font-size: 18px;
    }

    .about-us-num-m {
        position: absolute;
        top: 8%;
        left: 30%;
        text-align: center;
        width: 40%;
        display: flex;
        flex-direction: column;
    }

    .aboutus-con-m {
        position: absolute;
        width: 90%;
        left: 5%;
        top: 75%;
        text-align: center;
        font-size: 14px;
    }

    .new-p2-m {
        font-size: 12px;
    }

    .tab-slider-product .tabs > li a {
        background-color: #ddd;
        font-size: 20px;
    }

    .home-brick-box .brick-title {
        font-size: 36px;
        text-align: center;
        margin: 0;
        padding-bottom: 15px;
        font-weight: bold;
        line-height: 1.5;
    }

    .post-excerpt.text-left.more {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
    }

    @media screen and (max-width:989px) {
        .new-h1-m {
            font-size: 40px;
        }

        .new-h2-m {
            font-size: 24px;
        }

        .new-p2-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 15%;
        }

        .aboutus-con-m {
            top: 70%;
            font-size: 20px;
        }

        .slideshow__text-wrap .anim-tru.style1 {
            text-align: center;
            width: 100%;
        }
    }

    @media screen and (min-width:340px) and (max-width:576px) {
        .new-h1-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 6%;
        }

        .aboutus-con-m {
            font-size: 12px;
        }
    }

    @media screen and (min-width:200px) and (max-width:340px) {
        .new-h1-m {
            font-size: 20px;
        }

        .about-us-num-m {
            top: 6%;
        }

        .aboutus-con-m {
            font-size: 12px;
        }
    }
</style>
<style>
    .sw-nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px;
        height: 50px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 3;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

        .sw-nav-button:hover {
            background-color: rgba(255, 255, 255, 0.4);
        }

        .sw-nav-button.prev {
            left: 20px;
        }

        .sw-nav-button.next {
            right: 20px;
        }

        .sw-nav-button i {
            color: white;
            font-size: 24px;
        }

    .sw-container {
        --active-color: #fff;
        --inactive-color: #f5f5f569;
        position: relative;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        height: auto;
        aspect-ratio: 16/9;
        overflow: hidden;
        transition: height 0.3s ease;
    }

    .sw-slides {
        position: relative;
        width: 100%;
        height: 100%;
    }

    /*    .sw-slide {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

        .sw-slide.active {
            opacity: 1;
            z-index: 1;
        }

        .sw-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center center;
            display: block;
            backface-visibility: hidden;
        }*/

    .slide-content {
        /*        position: absolute;
        top: 22%;
        bottom: 22%;
        left: 8.6%;
        width: 670px;
        color: #fff;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        transition: all 0.3s ease;
        text-align:left;*/
    }

    .slide-title {
        color: #000;
        font-size: clamp(32px, 4vw, 56px);
        font-weight: 700;
        line-height: 1.2;
        margin: 0;
        padding-top: 20px;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .slide-desc {
        font-size: clamp(16px, 1.8vw, 26px);
        line-height: 1.4;
        margin: 0 0 2em 0;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .slide-button {
        display: inline-block;
        padding: 12px 40px;
        background: #FF6B00;
        color: #fff;
        font-size: clamp(14px, 1.5vw, 20px);
        font-weight: 700;
        border-radius: 30px;
        text-decoration: none;
        /*        opacity: 0;*/
        transform: translateY(30px);
        transition: all 0.6s 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;
        width: fit-content;
    }

    /*    .sw-slide.active .slide-title,
    .sw-slide.active .slide-desc,
    .sw-slide.active .slide-button {
        opacity: 1;
        transform: translateY(0);
    }*/

    .sw-indicators {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
        display: flex;
        gap: 10px;
        padding: 5px;
        border-radius: 8px;
    }

    .sw-indicator {
        width: 40px;
        height: 3px;
        background: var(--inactive-color);
        cursor: pointer;
        transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: left center;
    }

        .sw-indicator.active {
            background: var(--active-color);
            transform: scaleX(1);
        }

    /* 周年庆banner样式 */
    .anni-bg-img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 0;
    }

    .anni-particles-bg {
        position: relative;
        height: 100%;
        width: 100%;
        z-index: 1;
    }

        .anni-particles-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /*background-color: rgba(0, 0, 0, 0.3);*/
            z-index: -1;
        }

    .anni-particle {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.5);
        animation: anni-float 5s infinite ease-in-out;
        z-index: 1;
    }

    @keyframes anni-float {

        0%, 100% {
            transform: translateY(0) translateX(0);
            opacity: 0.8;
        }

        50% {
            transform: translateY(-20px) translateX(10px);
            opacity: 0.3;
        }
    }

    .anni-content {
        position: relative;
        z-index: 2;
        width: 100%;
        padding: 0 20px;
        text-align: center;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .anni-main-title {
        font-size: clamp(2rem, 4vw, 4rem);
        font-weight: 800;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        margin-bottom: 1rem;
        letter-spacing: -1px;
        line-height: 1.2;
        color: white;
    }

    .anni-sub-title {
        font-size: clamp(1rem, 1.5vw, 1.5rem);
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        margin-bottom: 2rem;
        max-width: 500px;
    }

    .anni-countdown-container {
        margin: 2rem 0;
    }

    .anni-countdown-title {
        font-size: clamp(14px, 1.5vw, 25px);
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.85);
    }

    .anni-countdown-box {
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .anni-countdown-item {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.15);
        border-radius: 10px;
        padding: clamp(15px, 2vw, 25px) clamp(15px, 2vw, 35px);
        min-width: 60px;
        text-align: center;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .anni-countdown-value {
        font-size: clamp(1.5rem, 3vw, 2.2rem);
        font-weight: 800;
        line-height: 1;
        letter-spacing: -1px;
    }

    .anni-countdown-label {
        font-size: clamp(0.7rem, 1.5vw, 0.85rem);
        opacity: 0.9;
    }

    .anni-cta-btn {
        background: linear-gradient(135deg, #FF6B00 0%, #e05a00 100%);
        color: white;
        border: none;
        padding: clamp(10px, 1.5vw, 14px) clamp(20px, 2.5vw, 36px);
        font-size: clamp(1rem, 1.5vw, 1.2rem);
        font-weight: 600;
        border-radius: 50px;
        box-shadow: 0 4px 15px rgba(255, 107, 0, 0.4);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        margin-top: 10px;
        text-decoration: none;
        display: inline-block;
    }

        .anni-cta-btn:hover {
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 0, 0.6);
        }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .sw-container {
            aspect-ratio: 3/4;
            max-height: 100vh;
        }

        /*        .sw-slide,
        .sw-slide img,
        .anni-particles-bg {
            height: 100% !important;
        }*/
        /* 周年庆内容区域调整 */
        .anni-content {
            padding: 0% 15px 0%;
            justify-content: flex-start;
            transform: none;
            left: 0;
            top: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .anni-main-title {
            font-size: 1.5rem !important;
            line-height: 1.3;
            margin-bottom: 0.8rem;
            padding: 0 10px;
        }

        .anni-countdown-container {
            margin: 0rem auto;
            width: 100%;
            max-width: 220px;
        }

        .anni-countdown-box {
            gap: 5px;
        }

        .anni-countdown-item {
            padding: 8px 5px !important;
            min-width: 40px;
            flex-grow: 1;
        }

        .anni-countdown-value {
            font-size: 1.1rem;
        }

        .anni-countdown-label {
            font-size: 0.6rem;
        }

        .anni-cta-btn {
            padding: 8px 20px;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        /* 其他slide样式 */
        .slide-content {
            left: 50%;
            width: 90%;
            transform: translateX(-50%);
            top: 3%;
            bottom: auto;
            padding: 0 5% 20px;
            min-height: auto;
        }

        .slide-title {
            font-size: clamp(22px, 6vw, 28px);
            padding-top: 5px;
            margin-bottom: 8px;
        }

        .slide-desc {
            font-size: clamp(14px, 3.2vw, 16px);
            margin: 8px 0 12px;
            line-height: 1.5;
        }

        .slide-button {
            padding: 8px 20px;
            font-size: clamp(14px, 3.5vw, 16px);
            margin: 15px auto 10px;
            background: #ff6b0036 !important;
            border: 3px solid #ff6b00;
            color: #ff6b00;
            font-weight: 700;
        }

        .sw-indicator {
            width: 30px;
            height: 2px;
        }

        .sw-indicators {
            bottom: 15px;
        }

        .sw-nav-button {
            width: 40px;
            height: 40px;
        }

            .sw-nav-button i {
                font-size: 18px;
            }
    }

    /* 小屏幕手机特殊适配 */
    @media (max-width: 480px) {
        .anni-main-title {
            font-size: 1.3rem !important;
        }

        .anni-countdown-item {
            padding: 6px 3px !important;
        }

        .anni-countdown-value {
            font-size: 1rem;
        }
    }
</style>
<!--  弹窗样式  -->
<style>
    #newsletter-modal .coupon_info_box .coupon_info_title {
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
    }
    #newsletter-modal .coupon_info_box .coupon_info_content {
        margin-top: 12px;
        font-size: 18px;
        color: #ffffff;
    }
    #newsletter-modal .coupon_info_box .coupon_info_discounted {
        padding: 10px 0;
        background: url({{static_path}}/assets/images/newsletter/coupon_info_bg.png) no-repeat center;
        min-height: 117px;
        margin-top: 33px;
        box-sizing: border-box;
    }
        #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_discount {
            color: #242424;
            font-size: 18px;
        }
        #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_price {
            margin-top: 8px;
            color: #ff0000;
            font-size: 30px;
            font-weight: bold;
            line-height: 1;
        }
        #newsletter-modal .coupon_info_box .coupon_info_discounted .coupon_date {
            margin-top: 8px;
            color: #242424;
            font-size: 14px;
        }
    #newsletter-modal .coupon_info_box .coupon_info_btn {
        display: inline-block;
        margin-top: 22px;
        width:100%;
        line-height: 50px;
        max-width: 240px;
        text-align: center;
        border-radius: 5px;
        cursor: pointer;
        font-size: 24px;
    }
</style>
<script type="text/javascript">

    // 选择所有具有类名 'LoadMore' 的元素
    //const buttons = document.querySelectorAll('.LoadMore');

    // 为每个按钮添加点击事件监听器
    //buttons.forEach(function (button) {
    //    button.addEventListener('click', function () {
    //        if (button.previousElementSibling.classList.contains('more')) {
    //            button.previousElementSibling.classList.remove('more');
    //            button.textContent = "See Less";
    //        } else {
    //            button.previousElementSibling.classList.add('more');
    //            button.textContent = "Read More";

    //        }
    //    });
    //});

    function newsletter_popup() {
        var modal = document.querySelector('#newsletter-modal');
        var fademodal = document.querySelector('#newsletter-modal-fade');
        setTimeout(function () {
            modal.style.display = 'block';
            modal.classList.add('mfp-zoom-in');
            modal.classList.remove('mfp-hide');
            fademodal.classList.remove('mfp-hide');

            // 关闭按钮逻辑
            var closeButton = modal.querySelector('.mfp-close');
            if (closeButton || closeButtonclick) {
                closeButton.addEventListener('click', function () {
                    modal.style.display = 'none';
                    fademodal.style.display = 'none';
                });
            }
        }, 3000);
        var newslettercloseBtn = document.querySelector('.coupon_info_btn');
        newslettercloseBtn.addEventListener('click', function () {
            modal.style.display = 'none';
            fademodal.style.display = 'none';
        });

    }

</script>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        // 创建粒子效果
        function createParticles() {
            var particlesBg = document.querySelector('.anni-particles-bg');
            if (!particlesBg) return;

            var particleCount = 30;
            for (var i = 0; i < particleCount; i++) {
                var particle = document.createElement('div');
                particle.className = 'anni-particle';

                var size = Math.random() * 5 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 5 + 's';

                particlesBg.appendChild(particle);
            }
        }

        // 倒计时
        //function updateCountdown() {
        //    var countdownDate = new Date('2025-07-21T10:00:00').getTime();
        //    var now = new Date().getTime();
        //    var distance = countdownDate - now;

        //    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        //    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        //    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        //    var seconds = Math.floor((distance % (1000 * 60)) / 1000);

        //    document.getElementById('anni-days').textContent = days.toString().padStart(2, '0');
        //    document.getElementById('anni-hours').textContent = hours.toString().padStart(2, '0');
        //    document.getElementById('anni-minutes').textContent = minutes.toString().padStart(2, '0');
        //    document.getElementById('anni-seconds').textContent = seconds.toString().padStart(2, '0');
        //}

        // 初始化粒子效果和倒计时
        createParticles();
        //updateCountdown();
        //setInterval(updateCountdown, 1000);

        class ManualSlider {
            constructor(container) {
			if(container!=null)
			{
                this.container = container;
                this.slides = Array.from(container.querySelectorAll('.sw-slide'));
                this.images = Array.from(container.querySelectorAll('img'));
                this.images.forEach(img => img.dataset.pc = img.src);
                this.indicatorsContainer = container.querySelector('.sw-indicators');
                this.prevButton = container.querySelector('.sw-nav-button.prev');
                this.nextButton = container.querySelector('.sw-nav-button.next');
                this.currentIndex = 0;
                this.isMobile = this.detectMobile();
                this.touchStartX = 0;
                this.swipeThreshold = 50;
                this.resizeTimer = null;

                this.initImageSources();
                this.createIndicators();
                this.setupEventListeners();
                this.initAspectRatio();
                this.setInitialHeight();
                window.addEventListener('resize', this.handleResize.bind(this));
				}
            }

            setInitialHeight() {
                const firstImg = this.container.querySelector('.sw-slide.active img');
                if (firstImg && firstImg.complete) {
                    this.updateContainerRatio(firstImg);
                }
            }

            handleResize() {
                clearTimeout(this.resizeTimer);
                this.resizeTimer = setTimeout(() => {
                    this.setInitialHeight();
                }, 250);
            }

            detectMobile() {
                return window.matchMedia('(max-width: 768px)').matches;
            }

            initImageSources() {
                this.images.forEach(img => {
                    const targetSrc = this.isMobile ? img.dataset.mobile : img.dataset.pc;
                    if (img.src !== targetSrc) {
                        img.onload = () => this.updateAspectRatio();
                        img.src = targetSrc;
                    }
                });
            }

            initAspectRatio() {
                const activeImg = this.container.querySelector('.sw-slide.active img');
                if (activeImg && activeImg.complete) {
                    this.updateContainerRatio(activeImg);
                } else if (activeImg) {
                    activeImg.onload = () => this.updateContainerRatio(activeImg);
                }
            }

            updateContainerRatio(img) {
                const naturalWidth = img.naturalWidth;
                const naturalHeight = img.naturalHeight;
                if (naturalWidth > 0 && naturalHeight > 0) {
                    this.container.style.aspectRatio = `${naturalWidth}/${naturalHeight}`;
                }
            }

            createIndicators() {
                this.slides.forEach((_, index) => {
                    const indicator = document.createElement('div');
                    indicator.className = `sw-indicator ${index === 0 ? 'active' : ''}`;
                    indicator.addEventListener('click', () => this.goTo(index));
                    this.indicatorsContainer.appendChild(indicator);
                });
            }

            setupEventListeners() {
                const mediaQuery = window.matchMedia('(max-width: 768px)');
                mediaQuery.addEventListener('change', (e) => {
                    this.isMobile = e.matches;
                    this.initImageSources();
                    this.initAspectRatio();
                });

                window.addEventListener('resize', () => {
                    clearTimeout(this.resizeTimer);
                    this.resizeTimer = setTimeout(() => {
                        const wasMobile = this.isMobile;
                        this.isMobile = this.detectMobile();
                        if (wasMobile !== this.isMobile) {
                            this.initImageSources();
                            this.initAspectRatio();
                        }
                    }, 250);
                });

                // 添加导航按钮事件
                if (this.prevButton) {
                    this.prevButton.addEventListener('click', () => this.prev());
                }

                if (this.nextButton) {
                    this.nextButton.addEventListener('click', () => this.next());
                }

                this.container.addEventListener('touchstart', (e) => {
                    this.touchStartX = e.touches[0].clientX;
                }, { passive: true });

                this.container.addEventListener('touchend', (e) => {
                    const touchEndX = e.changedTouches[0].clientX;
                    const deltaX = touchEndX - this.touchStartX;
                    if (Math.abs(deltaX) > this.swipeThreshold) {
                        deltaX > 0 ? this.prev() : this.next();
                    }
                }, { passive: true });
            }

            goTo(index) {
                this.currentIndex = (index + this.slides.length) % this.slides.length;
                this.slides.forEach(slide => slide.classList.remove('active'));
                const activeSlide = this.slides[this.currentIndex];
                activeSlide.classList.add('active');

                const activeImg = activeSlide.querySelector('img');
                if (activeImg && activeImg.complete) {
                    this.updateContainerRatio(activeImg);
                } else if (activeImg) {
                    activeImg.onload = () => this.updateContainerRatio(activeImg);
                }

                this.updateIndicators();
            }

            updateIndicators() {
                this.indicatorsContainer.querySelectorAll('.sw-indicator').forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentIndex);
                });
            }

            next() {
                this.goTo(this.currentIndex + 1);
            }

            prev() {
                this.goTo(this.currentIndex - 1);
            }
        }

        // 初始化手动轮播
        const slider = new ManualSlider(document.querySelector('.sw-container'));

        // 确保添加高度强制刷新
        setTimeout(() => {
            const container = document.querySelector('.sw-container');
            if (container) container.style.height = 'auto';
        }, 50);

        // 预加载优化
        window.addEventListener('load', () => {
            document.querySelectorAll('.sw-slide:not(.active) img').forEach(img => {
                img.setAttribute('loading', 'lazy');
            });
        });
    });
</script>

<!-- 导入弹窗组件 -->
<script src="/js/Pop-ups/frame-message.js"></script>
<!-- 导入快速预览脚本 -->
<script src="/businessJs/Product/Index/quickView_t200.js"></script>

<script>
    // 设置静态资源路径全局变量，供quickView_t200.js使用
    window.staticPath = '{{ static_path }}';

    document.addEventListener('DOMContentLoaded', function () {
        document.addEventListener('click', function (e) {
            // 购物车按钮现在直接跳转到产品详情页面，不需要特殊处理

            // 处理收藏按钮点击事件
            const wishlistBtn = e.target.closest('.wishlist-btn .add-to-wishlist');
            if (wishlistBtn) {
                e.preventDefault();
                e.stopPropagation();

                // 直接从wishlist按钮获取产品ID和收藏状态
                const productId = wishlistBtn.getAttribute('data-product-id');
                const isFavorited = wishlistBtn.getAttribute('data-is-favorited') === 'true';

                if (!productId) {
                    if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                        customize_pop.warning('Unable to get product ID', null, null, {showIcon: false});
                    } else {
                        alert('Unable to get product ID');
                    }
                    return;
                }

                // 根据当前收藏状态决定操作
                const url = isFavorited ? '/Account/RemoveFromWishlistByProductId' : '/Account/AddToWishlist';
                const requestBody = {productId: parseInt(productId)};

                // 发送收藏请求
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 根据操作类型显示不同的成功消息
                        const message = isFavorited ? 'Removed from wishlist successfully' : 'Added to wishlist successfully';
                        if (typeof customize_pop !== 'undefined' && customize_pop.success) {
                            customize_pop.success(message, null, null, {showIcon: false});
                        } else {
                            alert(message);
                        }

                        // 更新按钮状态
                        const heartIcon = wishlistBtn.querySelector('i');
                        if (heartIcon) {
                            if (isFavorited) {
                                heartIcon.className = 'icon an an-heart-o'; // 改为空心心形
                                wishlistBtn.setAttribute('data-is-favorited', 'false');
                            } else {
                                heartIcon.className = 'icon an an-heart'; // 改为实心心形
                                wishlistBtn.setAttribute('data-is-favorited', 'true');
                            }
                        }
                    } else {
                        if (typeof customize_pop !== 'undefined' && customize_pop.warning) {
                            customize_pop.warning(data.message || 'Operation failed', null, null, {showIcon: false});
                        } else {
                            alert(data.message || 'Operation failed');
                        }
                    }
                })
                .catch(error => {
                    console.error('收藏操作时出错:', error);
                    if (typeof customize_pop !== 'undefined' && customize_pop.error) {
                        customize_pop.error('Network error occurred', null, null, {showIcon: false});
                    } else {
                        alert('Network error occurred');
                    }
                });
            }
        });
    });
</script>