# 可视化页面装修使用说明

## 概述

本文档说明如何在t200主题的Liquid模板中使用可视化页面装修数据。

## 可用的Liquid过滤器

### 1. get_visual_plugins
获取指定类型的可视化插件列表

```liquid
{% assign carousel_plugins = VisualPageData | get_visual_plugins: 'carousel' %}
{% assign banner_plugins = VisualPageData | get_visual_plugins: 'banner' %}
{% assign products_plugins = VisualPageData | get_visual_plugins: 'products' %}
```

### 2. get_plugin_settings
获取插件的设置数据

```liquid
{% for plugin in carousel_plugins %}
    {% assign plugin_settings = plugin | get_plugin_settings %}
    <!-- 使用设置数据 -->
    {% if plugin_settings.CarouselAuto %}
        <!-- 自动播放轮播图 -->
    {% endif %}
{% endfor %}
```

### 3. get_plugin_blocks
获取插件的内容块数据

```liquid
{% for plugin in carousel_plugins %}
    {% assign plugin_blocks = plugin | get_plugin_blocks %}
    {% for block_item in plugin_blocks %}
        {% assign block = block_item[1] %}
        <!-- 使用内容块数据 -->
        <img src="{{ block.PicPc }}" alt="{{ block.Title }}" />
    {% endfor %}
{% endfor %}
```

### 4. is_plugin_visible
检查插件是否应该显示

```liquid
{% for plugin in carousel_plugins %}
    {% assign plugin_visible = plugin | is_plugin_visible %}
    {% if plugin_visible %}
        <!-- 渲染插件内容 -->
    {% endif %}
{% endfor %}
```

### 5. get_theme_config
获取主题配置

```liquid
{% assign primary_color = VisualPageData | get_theme_config: 'ThemesBodyColor' %}
{% assign font_color = VisualPageData | get_theme_config: 'FontColor' %}
```

## 使用示例

### 轮播图插件使用示例

```liquid
<!--Home slider-->
<div class="visual_plugins_container" data-type="carousel" data-mode="mode_1">
    <div class="slideshow slideshow-wrapper">
        <div class="home-slideshow">
            {% comment %} 使用可视化数据渲染轮播图 {% endcomment %}
            {% assign carousel_plugins = VisualPageData | get_visual_plugins: 'carousel' %}
            {% if carousel_plugins.size > 0 %}
                {% for plugin in carousel_plugins %}
                    {% assign plugin_visible = plugin | is_plugin_visible %}
                    {% if plugin_visible %}
                        {% assign plugin_blocks = plugin | get_plugin_blocks %}
                        {% assign plugin_settings = plugin | get_plugin_settings %}
                        {% for block_item in plugin_blocks %}
                            {% assign block = block_item[1] %}
                            {% if block.ContentType == 'images' %}
                            <div class="slide">
                                <div class="blur-up lazyload">
                                    <img class="blur-up lazyload desktop-hide sw-slide" 
                                         data-src="{{ block.PicPc }}" 
                                         src="{{ block.PicPc }}" 
                                         alt="{{ block.ImageAltPc | default: block.Title }}" />
                                    {% if block.Title or block.Content or block.ButtonText %}
                                    <div class="slideshow__text-wrap slideshow__overlay {{ block.TextPosition | default: 'center' }}">
                                        <div class="slideshow__text-content">
                                            <div class="slide-content">
                                                {% if block.Title %}
                                                <h2 class="slide-title" style="color: {{ block.TitleColor | default: '#ffffff' }};">{{ block.Title }}</h2>
                                                {% endif %}
                                                {% if block.Content %}
                                                <p class="slide-desc" style="color: {{ block.ContentColor | default: '#ffffff' }};">{{ block.Content }}</p>
                                                {% endif %}
                                                {% if block.ButtonText and block.Link %}
                                                <a href="{{ block.Link }}" class="slide-button">{{ block.ButtonText }}</a>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% else %}
                <!-- 如果没有可视化数据，显示默认内容 -->
                <div class="slide">
                    <div class="blur-up lazyload">
                        <img src="{{static_path}}/assets/images/index/banner/default-banner.webp" alt="默认横幅" />
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
```

### 横幅插件使用示例

```liquid
<!-- Banner Section -->
{% assign banner_plugins = VisualPageData | get_visual_plugins: 'banner' %}
{% if banner_plugins.size > 0 %}
    {% for plugin in banner_plugins %}
        {% assign plugin_visible = plugin | is_plugin_visible %}
        {% if plugin_visible %}
            {% assign plugin_blocks = plugin | get_plugin_blocks %}
            {% for block_item in plugin_blocks %}
                {% assign block = block_item[1] %}
                <div class="banner-section">
                    <img src="{{ block.PicPc }}" alt="{{ block.Title }}" />
                    {% if block.Title %}
                        <h3>{{ block.Title }}</h3>
                    {% endif %}
                    {% if block.Content %}
                        <p>{{ block.Content }}</p>
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}
    {% endfor %}
{% endif %}
```

### 产品展示插件使用示例

```liquid
<!-- Products Section -->
{% assign products_plugins = VisualPageData | get_visual_plugins: 'products' %}
{% if products_plugins.size > 0 %}
    {% for plugin in products_plugins %}
        {% assign plugin_visible = plugin | is_plugin_visible %}
        {% if plugin_visible %}
            {% assign plugin_settings = plugin | get_plugin_settings %}
            {% assign plugin_blocks = plugin | get_plugin_blocks %}
            <div class="products-section">
                <div class="container">
                    <div class="row">
                        <!-- 根据插件配置渲染产品 -->
                        {% comment %} 这里可以根据plugin_blocks中的产品ID获取产品数据 {% endcomment %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% endif %}
```

### 主题配置使用示例

```liquid
<!-- 应用主题配置 -->
<style>
    :root {
        --primary-color: {{ VisualPageData | get_theme_config: 'ThemesBodyColor' | default: '#ffffff' }};
        --font-color: {{ VisualPageData | get_theme_config: 'FontColor' | default: '#333333' }};
        --price-color: {{ VisualPageData | get_theme_config: 'PriceColor' | default: '#E00000' }};
        --button-bg-color: {{ VisualPageData | get_theme_config: 'AddtoCartBgColor' | default: '#fed925' }};
    }
    
    body {
        background-color: var(--primary-color);
        color: var(--font-color);
    }
    
    .price {
        color: var(--price-color);
    }
    
    .btn-primary {
        background-color: var(--button-bg-color);
    }
</style>
```

## 注意事项

1. 在使用可视化数据之前，请确保检查数据是否存在
2. 为没有可视化数据的情况提供默认内容
3. 使用 `default` 过滤器为配置项提供默认值
4. 检查插件的可见性状态
5. 正确解析JSON格式的配置数据

## 调试

如果需要查看可视化数据的结构，可以在模板中添加：

```liquid
<!-- 调试信息 -->
{% if VisualPageData %}
    <script>
        console.log('Visual Page Data:', {{ VisualPageData | json }});
    </script>
{% endif %}
```
