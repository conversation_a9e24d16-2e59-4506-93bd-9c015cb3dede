/**
 * 可视化页面构建器 JavaScript 库
 * 用于前台页面与后台可视化装修数据的交互
 */

class VisualPageBuilder {
    constructor() {
        this.currentPage = 'index';
        this.visualData = null;
        this.pluginsByType = {};
    }

    /**
     * 初始化可视化页面
     * @param {string} pages - 页面类型
     */
    async init(pages = 'index') {
        this.currentPage = pages;
        try {
            await this.loadPageData();
            this.renderPage();
        } catch (error) {
            console.error('初始化可视化页面失败:', error);
        }
    }

    /**
     * 加载页面数据
     */
    async loadPageData() {
        try {
            const response = await fetch(`/Visual/GetPageData?pages=${this.currentPage}`);
            const result = await response.json();
            
            if (result.ret === 1) {
                this.visualData = result.data;
                this.pluginsByType = result.data.pluginsByType || {};
                console.log('页面数据加载成功:', this.visualData);
            } else {
                console.error('加载页面数据失败:', result.msg);
            }
        } catch (error) {
            console.error('请求页面数据失败:', error);
        }
    }

    /**
     * 渲染页面
     */
    renderPage() {
        if (!this.visualData || !this.visualData.plugins) {
            console.warn('没有可用的页面数据');
            return;
        }

        // 按插件类型渲染不同的组件
        Object.keys(this.pluginsByType).forEach(type => {
            this.renderPluginsByType(type, this.pluginsByType[type]);
        });
    }

    /**
     * 根据插件类型渲染插件
     * @param {string} type - 插件类型
     * @param {Array} plugins - 插件列表
     */
    renderPluginsByType(type, plugins) {
        console.log(`渲染插件类型: ${type}`, plugins);
        
        switch (type) {
            case 'carousel':
                this.renderCarousel(plugins);
                break;
            case 'banner':
                this.renderBanner(plugins);
                break;
            case 'products':
                this.renderProducts(plugins);
                break;
            case 'text':
                this.renderText(plugins);
                break;
            default:
                console.log(`未知插件类型: ${type}`);
        }
    }

    /**
     * 渲染轮播图插件
     * @param {Array} plugins - 轮播图插件列表
     */
    renderCarousel(plugins) {
        plugins.forEach(plugin => {
            try {
                const settings = JSON.parse(plugin.settings || '{}');
                const blocks = JSON.parse(plugin.blocks || '{}');
                
                console.log('轮播图设置:', settings);
                console.log('轮播图内容:', blocks);
                
                // 在这里实现轮播图的渲染逻辑
                this.createCarouselElement(plugin, settings, blocks);
            } catch (error) {
                console.error('解析轮播图数据失败:', error);
            }
        });
    }

    /**
     * 创建轮播图元素
     * @param {Object} plugin - 插件数据
     * @param {Object} settings - 设置数据
     * @param {Object} blocks - 内容数据
     */
    createCarouselElement(plugin, settings, blocks) {
        // 查找轮播图容器
        const container = document.querySelector(`[data-plugin-type="carousel"]`);
        if (!container) {
            console.warn('未找到轮播图容器');
            return;
        }

        // 创建轮播图HTML
        let carouselHtml = '<div class="visual-carousel" data-plugin-id="' + plugin.pId + '">';
        
        // 遍历轮播图内容块
        Object.keys(blocks).forEach(blockKey => {
            const block = blocks[blockKey];
            if (block.ContentType === 'images') {
                carouselHtml += `
                    <div class="carousel-item">
                        <img src="${block.PicPc}" alt="${block.ImageAltPc || ''}" />
                        <div class="carousel-content">
                            <h2>${block.Title || ''}</h2>
                            <p>${block.Content || ''}</p>
                            ${block.ButtonText ? `<button class="carousel-btn">${block.ButtonText}</button>` : ''}
                        </div>
                    </div>
                `;
            }
        });
        
        carouselHtml += '</div>';
        container.innerHTML = carouselHtml;
    }

    /**
     * 渲染横幅插件
     * @param {Array} plugins - 横幅插件列表
     */
    renderBanner(plugins) {
        plugins.forEach(plugin => {
            try {
                const settings = JSON.parse(plugin.settings || '{}');
                const blocks = JSON.parse(plugin.blocks || '{}');
                
                console.log('横幅设置:', settings);
                console.log('横幅内容:', blocks);
                
                // 在这里实现横幅的渲染逻辑
            } catch (error) {
                console.error('解析横幅数据失败:', error);
            }
        });
    }

    /**
     * 渲染产品插件
     * @param {Array} plugins - 产品插件列表
     */
    renderProducts(plugins) {
        plugins.forEach(plugin => {
            try {
                const settings = JSON.parse(plugin.settings || '{}');
                const blocks = JSON.parse(plugin.blocks || '{}');
                
                console.log('产品设置:', settings);
                console.log('产品内容:', blocks);
                
                // 在这里实现产品展示的渲染逻辑
            } catch (error) {
                console.error('解析产品数据失败:', error);
            }
        });
    }

    /**
     * 渲染文本插件
     * @param {Array} plugins - 文本插件列表
     */
    renderText(plugins) {
        plugins.forEach(plugin => {
            try {
                const settings = JSON.parse(plugin.settings || '{}');
                const blocks = JSON.parse(plugin.blocks || '{}');
                
                console.log('文本设置:', settings);
                console.log('文本内容:', blocks);
                
                // 在这里实现文本的渲染逻辑
            } catch (error) {
                console.error('解析文本数据失败:', error);
            }
        });
    }

    /**
     * 获取指定类型的插件
     * @param {string} type - 插件类型
     * @returns {Array} 插件列表
     */
    getPluginsByType(type) {
        return this.pluginsByType[type] || [];
    }

    /**
     * 获取当前页面的主题配置
     * @returns {Object} 主题配置
     */
    getThemeConfig() {
        if (!this.visualData || !this.visualData.draft) {
            return {};
        }

        try {
            return JSON.parse(this.visualData.draft.config || '{}');
        } catch (error) {
            console.error('解析主题配置失败:', error);
            return {};
        }
    }

    /**
     * 应用主题样式
     */
    applyThemeStyles() {
        const themeConfig = this.getThemeConfig();
        
        // 应用主题颜色和样式
        if (themeConfig.ThemesBodyColor) {
            document.body.style.backgroundColor = themeConfig.ThemesBodyColor;
        }
        
        // 可以根据需要添加更多主题样式应用逻辑
        console.log('应用主题配置:', themeConfig);
    }
}

// 全局实例
window.visualPageBuilder = new VisualPageBuilder();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以从页面元素或URL中获取页面类型
    const pageType = document.body.getAttribute('data-page-type') || 'index';
    window.visualPageBuilder.init(pageType);
});
