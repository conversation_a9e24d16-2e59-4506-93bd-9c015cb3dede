<Project Sdk="Microsoft.NET.Sdk.Web">

  
  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  
  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Compile Remove="wwwroot\static\themes\t600\assets\img\新文件夹1\**" />
    <Content Remove="Logs\**" />
    <Content Remove="wwwroot\static\themes\t600\assets\img\新文件夹1\**" />
    <EmbeddedResource Remove="Logs\**" />
    <EmbeddedResource Remove="wwwroot\static\themes\t600\assets\img\新文件夹1\**" />
    <None Remove="Logs\**" />
    <None Remove="wwwroot\static\themes\t600\assets\img\新文件夹1\**" />
    <Content Remove="wwwroot\assets\js\global_page.js~RF1e9287c.TMP" />
    <None Remove="Views\Ajax\AjaxCoupon.liquid" />
    <None Remove="Views\Order\Address.liquid~RF17ed379a.TMP" />
    <None Remove="Views\Themes\t600\Account\MyContact.liquid" />
    <None Remove="Views\Themes\t600\Header.liquid~RF2f68f91f.TMP" />
    <None Remove="Views\Themes\t600\SignUpSuccess.liquid~RF42cb0f.TMP" />
    <None Update="Views\Themes\t600\Blog\Back\Index_back.liquid">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Views\Themes\t600\Blog\Back\Detail_back.liquid">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Views\Themes\t200\Shop\Back\RecentlyProductSlider_Back.liquid">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Views\Themes\t200\Shop\Back\RelatedProductSlider_Back.liquid">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Remove="Views\Themes\t200\HeaderFooterData.liquid" />
    <Content Remove="Views\Shared\_VisualPageLayout.cshtml" />
  </ItemGroup>

  
  <ItemGroup>
    <Compile Include="Temp\EmpTemp.liquid" />
  </ItemGroup>

  
  <ItemGroup>
    <Content Include="Views\Account\SignUpError.liquid" />
    <Content Include="Views\Account\SignUpSuccess.liquid" />
    <Content Include="Views\Account\SignInError.liquid" />
    <Content Include="Views\Ajax\AjaxCoupon.liquid" />
    <Content Include="Views\Themes\t100\Account\MyInbox.liquid" />
    <Content Include="Views\Themes\t100\Account\MyContact.liquid" />
    <Content Include="Views\Themes\t120\Account\MyContact.liquid" />
    <Content Include="Views\Themes\t120\Account\MyInbox.liquid" />
    <Content Include="Views\Themes\t200\Account\MyInbox.liquid" />
    <Content Include="Views\Themes\t200\Account\MyContact.liquid" />
    <Content Include="Views\Themes\t600\Account\MyInbox.liquid" />
    <Content Include="Views\Themes\t600\Account\MyContact.liquid" />
  </ItemGroup>
 
	<ItemGroup>
		 
		<None Update="**/*.liquid">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="fluid.core" Version="2.24.0" />
	  <PackageReference Include="Fluid.MvcViewEngine" Version="2.24.0" />
	  <PackageReference Include="I18Next.Net.AspNetCore" Version="1.0.0" />
	  <PackageReference Include="MessagePack" Version="3.1.3" />
	  <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
	  <PackageReference Include="Wangkanai.Detection" Version="8.17.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\YseStore.Extensions\YseStore.Extensions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\static\themes\t100\assets\video\" />
    <Folder Include="wwwroot\static\themes\t200\assets\images\index\newArrival\" />
    <Folder Include="wwwroot\static\themes\t300\" />
    <Folder Include="wwwroot\static\themes\t600\assets\fonts\" />
    <Folder Include="wwwroot\static\themes\t600\assets\img\logo\" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="wwwroot\businessJs\Product\Comments\product-comments.js" />
    <_ContentIncludedByDefault Remove="wwwroot\businessJs\Product\ShopList\shopList_t200.js" />
  </ItemGroup>

</Project>

