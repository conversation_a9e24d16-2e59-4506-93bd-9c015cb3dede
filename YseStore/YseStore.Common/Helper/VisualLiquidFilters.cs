using Fluid;
using Fluid.Values;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Response.Visual;
using Entitys;

namespace YseStore.Common.Helper
{
    /// <summary>
    /// 可视化页面Liquid过滤器
    /// </summary>
    public static class VisualLiquidFilters
    {
        /// <summary>
        /// 获取指定类型的可视化插件
        /// </summary>
        /// <param name="input">可视化页面数据</param>
        /// <param name="arguments">参数：插件类型</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> GetVisualPlugins(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (arguments.Count == 0)
                {
                    return new ValueTask<FluidValue>(NilValue.Instance);
                }

                var pluginType = arguments.At(0).ToStringValue();
                if (string.IsNullOrEmpty(pluginType))
                {
                    return new ValueTask<FluidValue>(NilValue.Instance);
                }

                // 从上下文中获取可视化数据
                if (context.AmbientValues.TryGetValue("VisualPageData", out var visualDataObj) && 
                    visualDataObj is VisualPageResponse visualData)
                {
                    if (visualData.PluginsByType.ContainsKey(pluginType))
                    {
                        var plugins = visualData.PluginsByType[pluginType];
                        return new ValueTask<FluidValue>(FluidValue.Create(plugins, context.Options));
                    }
                }

                return new ValueTask<FluidValue>(FluidValue.Create(new List<visual_plugins>(), context.Options));
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(NilValue.Instance);
            }
        }

        /// <summary>
        /// 获取插件的设置数据
        /// </summary>
        /// <param name="input">插件对象</param>
        /// <param name="arguments">参数</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> GetPluginSettings(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (input.ToObjectValue() is visual_plugins plugin && !string.IsNullOrEmpty(plugin.Settings))
                {
                    var settings = JsonConvert.DeserializeObject<Dictionary<string, object>>(plugin.Settings);
                    return new ValueTask<FluidValue>(FluidValue.Create(settings, context.Options));
                }

                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
        }

        /// <summary>
        /// 获取插件的内容块数据
        /// </summary>
        /// <param name="input">插件对象</param>
        /// <param name="arguments">参数</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> GetPluginBlocks(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (input.ToObjectValue() is visual_plugins plugin && !string.IsNullOrEmpty(plugin.Blocks))
                {
                    var blocks = JsonConvert.DeserializeObject<Dictionary<string, object>>(plugin.Blocks);
                    return new ValueTask<FluidValue>(FluidValue.Create(blocks, context.Options));
                }

                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
        }

        /// <summary>
        /// 获取插件的配置数据
        /// </summary>
        /// <param name="input">插件对象</param>
        /// <param name="arguments">参数</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> GetPluginConfig(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (input.ToObjectValue() is visual_plugins plugin && !string.IsNullOrEmpty(plugin.Config))
                {
                    var config = JsonConvert.DeserializeObject<Dictionary<string, object>>(plugin.Config);
                    return new ValueTask<FluidValue>(FluidValue.Create(config, context.Options));
                }

                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(FluidValue.Create(new Dictionary<string, object>(), context.Options));
            }
        }

        /// <summary>
        /// 检查插件是否显示
        /// </summary>
        /// <param name="input">插件对象</param>
        /// <param name="arguments">参数</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> IsPluginVisible(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (input.ToObjectValue() is visual_plugins plugin && !string.IsNullOrEmpty(plugin.Config))
                {
                    var config = JsonConvert.DeserializeObject<Dictionary<string, object>>(plugin.Config);
                    if (config.ContainsKey("Display"))
                    {
                        var display = config["Display"]?.ToString();
                        return new ValueTask<FluidValue>(BooleanValue.Create(display == "1"));
                    }
                }

                return new ValueTask<FluidValue>(BooleanValue.True);
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(BooleanValue.True);
            }
        }

        /// <summary>
        /// 获取主题配置
        /// </summary>
        /// <param name="input">可视化页面数据</param>
        /// <param name="arguments">参数：配置键名</param>
        /// <param name="context">模板上下文</param>
        /// <returns></returns>
        public static ValueTask<FluidValue> GetThemeConfig(FluidValue input, FilterArguments arguments, TemplateContext context)
        {
            try
            {
                if (arguments.Count == 0)
                {
                    return new ValueTask<FluidValue>(NilValue.Instance);
                }

                var configKey = arguments.At(0).ToStringValue();
                if (string.IsNullOrEmpty(configKey))
                {
                    return new ValueTask<FluidValue>(NilValue.Instance);
                }

                // 从上下文中获取可视化数据
                if (context.AmbientValues.TryGetValue("VisualPageData", out var visualDataObj) && 
                    visualDataObj is VisualPageResponse visualData &&
                    visualData.Draft != null && !string.IsNullOrEmpty(visualData.Draft.Config))
                {
                    var themeConfig = JsonConvert.DeserializeObject<Dictionary<string, object>>(visualData.Draft.Config);
                    if (themeConfig.ContainsKey(configKey))
                    {
                        return new ValueTask<FluidValue>(FluidValue.Create(themeConfig[configKey], context.Options));
                    }
                }

                return new ValueTask<FluidValue>(NilValue.Instance);
            }
            catch (Exception)
            {
                return new ValueTask<FluidValue>(NilValue.Instance);
            }
        }
    }
}
